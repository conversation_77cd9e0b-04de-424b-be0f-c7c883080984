<template>
  <div class="modal fade" id="ModalDiscart" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
      <div class="modal-content modal-content-custom">
        <div class="header">
          <h4 class="modal-title text-title" id="exampleModalLabel">
            Descartar alterações
          </h4>
          <button
            type="button"
            class="close"
            data-dismiss="modal"
            aria-label="Close"
          >
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body body">
          <p class="text-body">Alterações não salvas.</p>
          <p class="text-body">Deseja salvar ou descartar essas alterações?</p>
        </div>
        <div class="footer">
          <div>
            <button type="button" class="btn btn-default" data-dismiss="modal">
              Cancelar
            </button>
          </div>
          <div>
            <button type="button" class="btn btn-danger" @click="closeModal()">
              <i class="glyphicon glyphicon-remove icon"></i> Descartar
            </button>
            <button type="button" class="btn btn-success" @click="salvar">
              <i class="glyphicon glyphicon-ok icon"></i> Salvar
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModalDiscart',
  props: {
    selectedItemId: {
      required: true,
      default: String,
    },
  },
  data() {
    return {
      ncmId: null,
    };
  },
  mounted() {
    this.ncmId = this.selectedItemId;
  },
  methods: {
    closeModal() {
      this.$emit('closeCollapse', { item: this.selectedItemId });
    },
    salvar() {
      this.$emit('salvarItens');
    },
  },
};
</script>

<style scoped>
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #e5e5e5;
}
.body {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f9fa;
  padding: 25px 15px;
}

.icon {
  font-size: smaller;
  margin-right: 10px;
}

.text-title {
  color: #8d9296;
  font-weight: 600;
}

.text-body {
  font-size: 20px;
  font-weight: 500;
  color: #4d545b;
  margin: 0;
}

.footer {
  border-top: 1px solid #e5e5e5;
  padding: 15px;
  display: flex;
  justify-content: space-between;
}

.modal-dialog-centered {
  height: 100vh;
  overflow-y: hidden;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.modal-content-custom {
  width: 100%;
}

.btn-success {
  background-color: #1d8856 !important;
}
.btn-danger {
  background-color: #b00302 !important;
}
</style>
